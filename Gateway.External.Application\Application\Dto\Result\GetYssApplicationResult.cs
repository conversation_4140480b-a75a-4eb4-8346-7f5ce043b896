﻿using System.Security.AccessControl;
using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses.Models;
using Gateway.External.Resources;

namespace Gateway.External.Application.Application.Dto.Result
{
    public class GetYssApplicationResult : BaseServiceResult<GetYssApplicationStatus>
    {
        public YssApplicationDto Data { get; set; }
    }

    public enum GetYssApplicationStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,

        [CustomHttpStatus(Code = "PRE_CONDITION_FAILED", Resources = typeof(ServiceResources), Status = "PRE_CONDITION_FAILED", StatusCode = HttpStatusCodes.PreConditionFailed)]
        PreConditionFailed,

        [CustomHttpStatus(Code = "BAD_REQUEST", Resources = typeof(ServiceResources), Status = "BAD_REQUEST", StatusCode = HttpStatusCodes.BadRequest)]
        BadRequest,

        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,

        [CustomHttpStatus(Code = "RESOURCE_ALREADY_EXİST", Resources = typeof(ServiceResources), Status = "RESOURCE_ALREADY_EXİST", StatusCode = HttpStatusCodes.ResourceExist)]
        AlreadyExist,

        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,

        [CustomHttpStatus(Code = "INTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError,

        [CustomHttpStatus(Code = "EXTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "EXTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.ServiceUnavailable)]
        ExternalServiceError
    }
}
