﻿using Gateway.Http;
using System.Text.Json.Serialization;

namespace Gateway.External.Application.DigitalInsurance.Dto
{
    public class BaseDigitalInsuranceResult : BaseHttpResponse { }

    public class DigitalInsuranceYssCheckResult : BaseDigitalInsuranceResult
    {
        [JsonPropertyName("data")]
        public YssDto Data { get; set; }
        public class YssDto
        {
            [JsonPropertyName("policyNo")]  
            public int PolicyNo { get; set; }

            [JsonPropertyName("productNo")] 
            public string ProductNo { get; set; }

            [JsonPropertyName("endorsNo")]  
            public int EndorsNo { get; set; }  
        }
    }

    public class DigitalInsuranceLoginResult : BaseDigitalInsuranceResult
    {
        [JsonPropertyName("data")]
        public LoginDto Data { get; set; }

        public class LoginDto
        {
            [JsonPropertyName("token")]
            public string Token { get; set; }

            [JsonPropertyName("firstname")]
            public string FirstName { get; set; }

            [JsonPropertyName("lastname")]
            public string LastName { get; set; }

            [JsonPropertyName("email")]
            public string Email { get; set; }

            [JsonPropertyName("firmName")]
            public string FirmName { get; set; }

            [JsonPropertyName("unitNo")]
            public string UnitNo { get; set; }
        }
    }

    public class DigitalInsuranceDocumentInfoResult : BaseDigitalInsuranceResult
    {
        [JsonPropertyName("data")]
        public CertificateDto Data { get; set; }
        public class CertificateDto
        {
            public byte[] Certificate { get; set; }
        }
    }
}
