﻿using Gateway.Extensions;
using Gateway.External.Application.Application;
using Gateway.External.Application.Application.Dto.Request;
using Gateway.External.Core.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Api.Models.Application;
using Gateway.External.Api.Models.Insurance;
using Gateway.External.Application.Insurance;
using Gateway.External.Application.Insurance.Dto;

namespace Gateway.External.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class InsuranceController : Controller
    {
        private readonly IContext _context;
        private readonly IInsuranceService _insuranceService;

        public InsuranceController(IContext context, IInsuranceService insuranceService)
        {
            _context = context;
            _insuranceService = insuranceService;
        }

        #region Public Methods


        /// <summary>
        /// Gets Application Statuses
        /// </summary>
        [SwaggerOperation(Summary = "Certificate Insurance", Description = "Certificate Insurance")]
        [HttpPost]
        [Route("insurance/certificate")]
        public async Task<IActionResult> CertificateInsurance(CertificateInsuranceRequestModel request)
        {
            var serviceRequest = new CertificateInsuranceRequest
            {
                PolicyNumber = request.PolicyNumber,
                EndorsNo = request.EndorsNo,
                ProductNo = request.ProductNo,
                PrintType = request.PrintType,
                Context = _context
            };

            var result = await _insuranceService.CertificateInsurance(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result?.Certificate);
        }

        #endregion
    }
}
