﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading.Tasks;

namespace Gateway.Http
{
    public class RestHttpClient
    {
        public static RestHttpClient Create()
        {
            return new RestHttpClient();
        }

        public async Task<T> Get<T>(string url, WebHeaderCollection headers) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;

                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var httpResponseMessage = await httpClient.GetAsync(url);

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Post<T>(string url, WebHeaderCollection headers) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;

                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var httpResponseMessage = await httpClient.PostAsync(url,null);

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Post<T>(string url, WebHeaderCollection headers, BaseHttpRequest request) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));
                                
                var httpResponseMessage = await httpClient.PostAsync(url, JsonBuilder.ToJsonString(request));

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Post<T>(string url, WebHeaderCollection headers, BaseHttpRequest request, bool serializeFormatting) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var httpResponseMessage = await httpClient.PostAsync(url, JsonBuilder.ToJsonString(request, serializeFormatting));

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Delete<T>(string url, WebHeaderCollection headers, BaseHttpRequest request) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var requestMessage = new HttpRequestMessage
                {
                    Content = JsonBuilder.ToJsonString(request),
                    Method = HttpMethod.Delete,
                    RequestUri = new Uri(url)
                };

                var httpResponseMessage = await httpClient.SendAsync(requestMessage);

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Delete<T>(string url, WebHeaderCollection headers) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var requestMessage = new HttpRequestMessage
                {
                    Method = HttpMethod.Delete,
                    RequestUri = new Uri(url)
                };

                var httpResponseMessage = await httpClient.SendAsync(requestMessage);

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Put<T>(string url, WebHeaderCollection headers, BaseHttpRequest request) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var httpResponseMessage = await httpClient.PutAsync(url, JsonBuilder.ToJsonString(request));

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Put<T>(string url, WebHeaderCollection headers, IEnumerable<BaseHttpRequest> request) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var httpResponseMessage = await httpClient.PutAsync(url, JsonBuilder.ToJsonString(request));

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> Patch<T>(string url, WebHeaderCollection headers, BaseHttpRequest request) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient(GetHttpClientHandler());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
                                                       SecurityProtocolType.Tls;
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

                var httpResponseMessage = await httpClient.PatchAsync(url, JsonBuilder.ToJsonString(request));

                var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

                return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            catch (Exception exc)
            {
                return GetErrorResponse<T>(exc, url);
            }
        }

        public async Task<T> PostForm<T>(string url, WebHeaderCollection headers, BaseHttpRequest request) where T : new()
        {
	        try
	        {
		        using var httpClient = new HttpClient(GetHttpClientHandler());

		        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 |
		                                               SecurityProtocolType.Tls;
		        httpClient.Timeout = TimeSpan.FromMinutes(2);

		        foreach (string key in headers.Keys) httpClient.DefaultRequestHeaders.Add(key, headers.Get(key));

		        var nvc = new List<KeyValuePair<string, string>>();

		        foreach (var prop in request.GetType().GetProperties())
		        {
			        nvc.Add(new KeyValuePair<string, string>(prop.Name, prop.GetValue(request, null)?.ToString()));
		        }

				var httpResponseMessage = await httpClient.PostAsync(url, new FormUrlEncodedContent(nvc));

		        var stream = await httpResponseMessage.Content.ReadAsStreamAsync();

		        return await JsonSerializer.DeserializeAsync<T>(stream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
	        }
	        catch (Exception exc)
	        {
		        return GetErrorResponse<T>(exc, url);
	        }
        }

		private static T GetErrorResponse<T>(Exception exc, string url, [CallerLineNumber] int line = 0,
            [CallerFilePath] string path = "", [CallerMemberName] string memberName = "") where T : new()
        {
            dynamic item = (T) Activator.CreateInstance(typeof(T));

            return (T) item;
        }

        private static HttpClientHandler GetHttpClientHandler()
        {
            return new HttpClientHandler();
        }

       
    }
}