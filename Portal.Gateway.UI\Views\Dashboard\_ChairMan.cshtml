﻿@using Microsoft.AspNetCore.Builder
@using Microsoft.Extensions.Options
@using Microsoft.AspNetCore.Http
@using Portal.Gateway.UI.Constants
@inject IHttpContextAccessor HttpContextAccessor
@inject IOptions<RequestLocalizationOptions> RequestLocalizationOptions

@{
    ViewBag.Title = SiteResources.Dashboard;
    var currentUser = Portal.Gateway.UI.Extensions.SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession);
}

<input type="hidden" id="statisticsLoaded" value="false" />
<input type="hidden" id="insuranceLoaded" value="false" />

<div class="row">
    <div class="col-xl-12">
        <div class="card card-custom gutter-b">
            <div class="card-header h-auto border-0 pr-0">
                <div class="card-title py-5">
                    <h3 class="card-label">
                        <span class="d-block text-dark font-weight-bolder">@SiteResources.Dashboard</span>
                        <span class="d-block text-muted mt-2 font-size-sm">@currentUser.Name @currentUser.Surname - @SiteResources.AllBranches.ToTitleCase()</span>
                    </h3>
                </div>
                <div class="card-toolbar">
                    <div class="dropdown dropdown-inline show-mobile pr-5">
                        <a class="btn btn-light dropdown-toggle text-dark-75" id="mobile-dropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">@SiteResources.GeneralPicture.ToTitleCase()</a>
                        <div class="dropdown-menu dropdown-menu-sm dropdown-menu-right" style="">
                            <ul class="navi navi-hover">
                                <li class="navi-item">
                                    <a onclick="changeTab(event, 'tab-general', '@SiteResources.GeneralPicture.ToTitleCase()')" class="navi-link active">
                                        <span class="navi-text">@SiteResources.GeneralPicture.ToTitleCase()</span>
                                    </a>
                                </li>
                                @*       <li class="navi-item">
                                    <a onclick="changeTab(event, 'tab-stats', '@SiteResources.Statistics.ToTitleCase()')" class="navi-link">
                                        <span class="navi-text">@SiteResources.Statistics.ToTitleCase()</span>
                                    </a>
                                </li>
                                <li class="navi-item">
                                    <a onclick="changeTab(event, 'tab-insurance', '@SiteResources.Insurance.ToTitleCase()')" class="navi-link">
                                        <span class="navi-text">@SiteResources.Insurance.ToTitleCase()</span>
                                    </a>
                                </li> *@
                                @*<li class="navi-item">
                                    <a onclick="changeTab(event, 'tab-portal')" class="navi-link">
                                        <span class="navi-text">@SiteResources.Portal.ToTitleCase()</span>
                                    </a>
                                </li>*@
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex mr-3 show-pc">
                        <div class="navi navi-hover navi-active navi-link-rounded navi-bold d-flex flex-row">
                            <div class="navi-item mr-2">
                                <a onclick="changeTab(event, 'tab-general')" class="navi-link active">
                                    <span class="navi-text">@SiteResources.GeneralPicture.ToTitleCase()</span>
                                </a>
                            </div>
                            @*      <div class="navi-item mr-2">
                                <a onclick="changeTab(event, 'tab-stats')" class="navi-link">
                                    <span class="navi-text">@SiteResources.Statistics.ToTitleCase()</span>
                                </a>
                            </div>
                            <div class="navi-item mr-2">
                                <a onclick="changeTab(event, 'tab-insurance')" class="navi-link">
                                    <span class="navi-text">@SiteResources.Insurance.ToTitleCase()</span>
                                </a>
                            </div> *@
                            @*<div class="navi-item mr-2">
                                <a onclick="changeTab(event, 'tab-portal')" class="navi-link">
                                    <span class="navi-text">@SiteResources.Portal.ToTitleCase()</span>
                                </a>
                            </div>*@
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tabcontent active" id="tab-general">
        <partial name="_ChairMan/_General/_GeneralStats" />
        @*  <partial name="_WorldMap" />
        <partial name="_ChairMan/_General/_DailyBranchStats" />
        <partial name="_ChairMan/_General/_QuarterApplicationStats" />
        <partial name="_ChairMan/_General/_QuarterApplicationCategoryStats" />
        <partial name="_ChairMan/_General/_FreeApplicationStats" />
        <partial name="_ChairMan/_General/_SocialMedia" /> *@
        @*<partial name="_ChairMan/_General/_QuarterApplicationStatusStats" />*@ @*cancelled by gateway*@
        @*<partial name="_ChairMan/_General/_TotalCountryApplicationStats" />*@
    </div>
    @* 
    <div class="tabcontent" id="tab-stats">
        <partial name="_ChairMan/_Statistics/_GeneralStats" />
        <partial name="_ChairMan/_Statistics/_AgeRangeStats" />
        <partial name="_ChairMan/_Statistics/_PrevDayApplicationTypeStats" />
        <partial name="_ChairMan/_Statistics/_PrevDayApplicationProcessTypeStats" />
        <partial name="_ChairMan/_Statistics/_PrevDayChangeStats" />
        <partial name="_ChairMan/_Statistics/_PrevDayApplicationStats" />
        <partial name="_ChairMan/_Statistics/_MonthlyExtraFeeStats" /> *@
    @*<partial name="_ChairMan/_Statistics/_MonthlyApplicationSummaryStats" />*@
    @*<partial name="_ChairMan/_Statistics/_QuarterExtraFeeStats" />*@
    @*<partial name="_ChairMan/_Statistics/_SatisfactionSurvey" />*@
    @* </div> *@

    @*     <div class="tabcontent" id="tab-insurance">
        <partial name="_ChairMan/_Insurance/_DailyInsuranceDetailedStats" />
        <partial name="_ChairMan/_Insurance/_AllBranchesActivePolicyStats" />
        <partial name="_ChairMan/_Insurance/_AllBranchesMonthlyInsuranceStats" />
        <partial name="_ChairMan/_Insurance/_AllBranchesQuarterInsuranceStats" />
        <partial name="_ChairMan/_Insurance/_QuarterInsuranceDetailedStats" /> *@
    @*<partial name="_ChairMan/_Insurance/_DailyInsuranceAgeRangeStats" />*@ @*cancelled by developer*@
    @*<partial name="_ChairMan/_Insurance/_QuarterInsuranceAgeRangeStats" />*@ @*cancelled by developer*@
    @* </div> *@

    @* <div class="tabcontent" id="tab-portal"> *@
    @*<partial name="_ChairMan/_Portal/_GeneralStats" />*@
    @*<partial name="_ChairMan/_Portal/_RatingOfInstitutions" />*@
    @*<partial name="_ChairMan/_Portal/_MermbershipApplicationStats" />*@
    @*<partial name="_ChairMan/_Portal/_AllAppointmentsThroughPortal" />*@
    @*<partial name="_ChairMan/_Portal/_InstitutionsTypeStats" />*@
    @* </div> *@

</div>

<div class="modal fade" id="modalInsuranceStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.AllBranchesQuarterInsuranceStats.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialInsuranceStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalActiveInsuranceStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.AllBranchesActivePolicyStats.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialActiveInsuranceStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalQuarterExtraFeeStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.AllBranchesQuarterInsuranceStats.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialQuarterExtraFeeStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalDailyApplicationProcessTypeStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.DailyApplicationProcessTpes.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialDailyApplicationProcessTypeStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalDailyApplicationTypeStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.DailyApplicationTypes.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialDailyApplicationTypeStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalDailyAgeRangeStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.DailyAgeRangeStats.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialDailyAgeRangeStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalDailyFreeApplicationStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.FreeApplicationsOfCountries.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialDailyFreeApplicationStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalQuarterApplicationCategoryStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.QuarterApplicationCategoryOfBranches.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialQuarterApplicationCategoryStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalQuarterApplicationStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.QuarterApplicationsOfBranches.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialQuarterApplicationStats"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalPreviousDayChangeStats" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.PreviousDayApplicationChangeStats.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialPreviousDayChangeStats"></div>
            </div>
        </div>
    </div>
</div>

<script>
    window.featureFlags = {
            EnableQuarterCategoryStatsGraph: @ViewBag.EnableQuarterCategoryStatsGraph.ToString().ToLower()
        };
</script>
<script src="~/js/Dashboard/chairman.js"></script>
