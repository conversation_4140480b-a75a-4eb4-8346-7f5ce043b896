﻿@using Microsoft.AspNetCore.Builder
@using Microsoft.Extensions.Caching.Memory
@using Microsoft.Extensions.Options
@using Microsoft.AspNetCore.Http
@using Portal.Gateway.UI.Constants
@inject IHttpContextAccessor HttpContextAccessor
@inject IMemoryCache memoryCache
@inject CurrentUserDataHelper currentUserDataHelper
@inject IOptions<RequestLocalizationOptions> RequestLocalizationOptions
@{
    var currentUser = Portal.Gateway.UI.Extensions.SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext.Session, SessionKeys.UserSession);
}

<div id="kt_header" class="header header-fixed">
    <div class="header-wrapper rounded-top-xl d-flex flex-grow-1 align-items-center">
        <div class="container-fluid d-flex align-items-center justify-content-end justify-content-lg-between flex-wrap">
            <div class="header-menu-wrapper header-menu-wrapper-left" id="kt_header_menu_wrapper">
                <div id="kt_header_menu" class="header-menu header-menu-mobile header-menu-layout-default">
                    <ul class="menu-nav">
                        <li class="menu-item menu-item-open menu-item-submenu menu-item-rel menu-item-open menu-dashboard" data-menu-toggle="click" aria-haspopup="true">
                            <a href="javascript:;" class="menu-link menu-toggle">
                                <span class="menu-text">@SiteResources.Dashboard</span>
                                <i class="menu-arrow"></i>
                            </a>
                            <div class="menu-submenu menu-submenu-classic menu-submenu-left">
                                <ul class="menu-subnav">
                                    <li class="menu-item" aria-haspopup="true">
                                        <a href="@Url.Action("ChairManSubMenu", "Dashboard", new { Area = ""})" class="menu-link">
                                            <span class="menu-text">Deneme</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        @{
                            var appointmentControllers = EnumHelper.GetEnumAsDictionary(typeof(AppointmentManagementControllers)).Select(x => new SelectListItem { Text = x.Value });
                            var menuModel = currentUserDataHelper.BuildMenuModel(currentUser);

                            if (!currentUser.IsValidAMS)
                            {
                                menuModel.MainMenuList = menuModel.MainMenuList.Where(q => !q.MenuTranslation.Any(p => p.Name == AppointmentManagementMenuItems.QMS.ToString())).ToList();
                            }
                            if (menuModel != null)
                            {
                                foreach (var mainMenu in menuModel.MainMenuList)
                                {
                                    <li class="menu-item menu-item-submenu menu-item-rel" data-menu-toggle="click" aria-haspopup="true">
                                        <a href="javascript:;" class="menu-link menu-toggle">
                                            <span class="menu-text">@mainMenu.MenuTranslation.First(p => p.LanguageId == Html.CurrentLanguageId().ToString().ToInt())?.Name.ToTitleCase()</span>
                                            <span class="menu-desc"></span>
                                            <i class="menu-arrow"></i>
                                        </a>
                                        <div class="menu-submenu menu-submenu-classic menu-submenu-left">
                                            <ul class="menu-subnav">
                                                @{
                                                    
                                                    if (!currentUser.IsValidAMS)
                                                    {
                                                        mainMenu.SubMenu = mainMenu.SubMenu.Where(q => !appointmentControllers.Any(p => p.Text == q.Action.Controller)).ToList();
                                                    }
                                                    foreach (var subMenu in mainMenu.SubMenu)
                                                    {
                                                        if (subMenu.Node.Count == 0)
                                                        {
                                                            <li class="menu-item" aria-haspopup="true">
                                                                <a href="@Url.Action(subMenu.Action.Method, subMenu.Action.Controller, new { Area = subMenu.Action.Area})" class="menu-link">
                                                                    <span class="menu-text">@subMenu.MenuTranslation.FirstOrDefault(p => p.LanguageId == Html.CurrentLanguageId().ToString().ToInt())?.Name.ToTitleCase()</span>
                                                                </a>
                                                            </li>
                                                        }
                                                        else
                                                        {
                                                            <li class="menu-item menu-item-submenu" data-menu-toggle="hover" aria-haspopup="true">
                                                                <a href="javascript:;" class="menu-link menu-toggle">
                                                                    <span class="menu-text">@subMenu.MenuTranslation.FirstOrDefault(p => p.LanguageId == Html.CurrentLanguageId().ToString().ToInt())?.Name.ToTitleCase()</span>
                                                                    <i class="menu-arrow"></i>
                                                                </a>

                                                                <div class="menu-submenu menu-submenu-classic menu-submenu-right">
                                                                    <ul class="menu-subnav">

                                                                        @{
                                                                            if (!currentUser.IsValidAMS)
                                                                            {
                                                                                subMenu.Node = subMenu.Node.Where(q => !appointmentControllers.Any(p => p.Text == q.Action.Controller)).ToList();
                                                                            }
                                                                            foreach (var node in subMenu.Node)
                                                                            {
                                                                                <li class="menu-item" aria-haspopup="true">
                                                                                    <a href="@Url.Action(node.Action.Method, node.Action.Controller, new { Area = node.Action.Area})" class="menu-link">
                                                                                        <span class="menu-text">@node.MenuTranslation.FirstOrDefault(p => p.LanguageId == Html.CurrentLanguageId().ToString().ToInt())?.Name.ToTitleCase()</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                        }
                                                                    </ul>
                                                                </div>
                                                            </li>
                                                        }
                                                    }
                                                }

                                            </ul>
                                        </div>
                                    </li>
                                }
                            }
                        }
                    </ul>
                </div>
            </div>

            @if (currentUser != null)
            {
                <div class="d-flex align-items-center mt-5 mb-5">
                    <div class="d-flex flex-column flex-grow-1 text-right">
                        <div class="mb-1">
                            <u class="font-weight-bold text-primary font-italic font-size-h5">@SiteResources.GatewayMotto</u>
                        </div>
                        <div class="mb-1">
                            <span class="font-weight-bold">@SiteResources.Hello @currentUser.FullName</span>
                            <span>@(!string.IsNullOrEmpty(currentUser.BranchName) ? $"({currentUser.BranchName})" : string.Empty)</span>
                            <a class="text-muted font-weight-bold" href="@Url.Action("Logout", "User", new { Area = "" })">(@SiteResources.Logout)</a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>
