﻿using Gateway.Http;

namespace Gateway.External.Application.DigitalInsurance.Dto
{
    public class DigitalInsuranceLoginRequest : BaseHttpRequest
    {
        public string UserName { get; set; }
        public string Password { get; set; }
    }
    public class DigitalInsurancePrintDocumentRequest:BaseHttpRequest
    {
        public string ProductNo { get; set; }
        public string PolicyNumber { get; set; }
        public int PrintType { get; set; }
        public string? EndorsNo { get; set; }
    }
}
